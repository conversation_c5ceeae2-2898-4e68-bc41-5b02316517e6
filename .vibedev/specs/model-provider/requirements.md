# Model Provider API 需求规格说明

## 执行摘要

本项目旨在使用Rust重写现有Go项目的Model Provider API，实现完整的模型提供商管理功能。该API允许用户创建、查看、更新、删除和搜索AI模型提供商配置，支持多种模型类型（OpenAI、Ollama等），确保与现有客户端完全兼容。

## 利益相关者

### 主要用户
- **系统管理员**: 需要配置和管理AI模型提供商
- **开发者**: 通过API集成模型提供商功能
- **最终用户**: 通过客户端使用配置的模型提供商

### 次要用户
- **运维人员**: 监控和维护模型提供商服务
- **安全团队**: 确保API密钥和敏感信息安全

## 功能性需求

### FR-001: 创建模型提供商
**描述**: 系统应允许用户创建新的模型提供商配置
**优先级**: 高
**验收标准**:
- [ ] 接受POST请求到`/model_provider/`端点
- [ ] 验证必填字段：name, api_type, base_url
- [ ] 自动设置builtin为false（非内置）
- [ ] 生成唯一ID并返回创建响应
- [ ] 支持的api_type包括：openai, ollama
- [ ] 返回格式：`{"_id": "generated_id", "result": "created"}`

### FR-002: 获取单个模型提供商
**描述**: 系统应允许用户通过ID获取特定模型提供商的详细信息
**优先级**: 高
**验收标准**:
- [ ] 接受GET请求到`/model_provider/:id`端点
- [ ] 验证ID参数存在性
- [ ] 返回完整的模型提供商对象
- [ ] 如果不存在返回404错误
- [ ] 过滤敏感字段（如api_key）在某些情况下

### FR-003: 更新模型提供商
**描述**: 系统应允许用户更新现有模型提供商的配置
**优先级**: 高
**验收标准**:
- [ ] 接受PUT请求到`/model_provider/:id`端点
- [ ] 验证模型提供商存在
- [ ] 保护内置提供商的关键字段（name, builtin）
- [ ] 保护系统字段（id, created, builtin）
- [ ] 清除相关缓存
- [ ] 返回格式：`{"_id": "id", "result": "updated"}`

### FR-004: 删除模型提供商
**描述**: 系统应允许用户删除非内置的模型提供商
**优先级**: 高
**验收标准**:
- [ ] 接受DELETE请求到`/model_provider/:id`端点
- [ ] 验证模型提供商存在
- [ ] 禁止删除内置提供商（builtin=true）
- [ ] 清除相关缓存
- [ ] 返回格式：`{"_id": "id", "result": "deleted"}`

### FR-005: 搜索模型提供商
**描述**: 系统应提供灵活的搜索功能来查找模型提供商
**优先级**: 高
**验收标准**:
- [ ] 支持GET `/model_provider/_search`（URL参数查询）
- [ ] 支持POST `/model_provider/_search`（请求体查询）
- [ ] 支持按name字段搜索
- [ ] 支持全文搜索（combined_fulltext）
- [ ] 支持分页（size, from参数）
- [ ] 支持排序
- [ ] 支持过滤（enabled, builtin等）
- [ ] 返回Elasticsearch兼容的响应格式

### FR-006: 模型配置管理
**描述**: 系统应支持每个提供商的模型列表配置
**优先级**: 中
**验收标准**:
- [ ] 支持models数组字段
- [ ] 每个模型包含name和可选的settings
- [ ] settings支持temperature, top_p, max_tokens等参数
- [ ] 验证模型配置格式

### FR-007: 缓存管理
**描述**: 系统应实现高效的缓存机制提升性能
**优先级**: 中
**验收标准**:
- [ ] 实现模型提供商对象缓存
- [ ] 缓存键格式：model_provider:{id}
- [ ] 缓存过期时间：30分钟
- [ ] 更新/删除时自动清除缓存
- [ ] 支持缓存预热

## 非功能性需求

### NFR-001: 性能要求
**描述**: API响应时间和吞吐量要求
**指标**:
- 单个查询响应时间 < 100ms（第95百分位）
- 搜索查询响应时间 < 200ms（第95百分位）
- 支持并发请求数 > 100 RPS
- 缓存命中率 > 80%

### NFR-002: 安全性要求
**描述**: 数据安全和访问控制要求
**标准**:
- 所有API端点需要身份认证
- 实现基于角色的权限控制（RBAC）
- API密钥字段加密存储
- 敏感字段在响应中过滤
- 输入验证防止注入攻击

### NFR-003: 兼容性要求
**描述**: 与现有系统的兼容性
**标准**:
- 100%兼容现有Go API的请求/响应格式
- 支持现有客户端无缝迁移
- 保持相同的错误码和错误消息格式
- 兼容现有的认证机制

### NFR-004: 可靠性要求
**描述**: 系统稳定性和错误处理
**标准**:
- 系统可用性 > 99.9%
- 优雅处理数据库连接失败
- 完整的错误日志记录
- 自动重试机制（适当场景）

## 数据模型

### ModelProvider结构
```rust
pub struct ModelProvider {
    pub id: String,                    // 唯一标识符
    pub created: DateTime<Utc>,        // 创建时间
    pub updated: DateTime<Utc>,        // 更新时间
    pub name: String,                  // 提供商名称
    pub api_key: String,               // API密钥
    pub api_type: String,              // API类型
    pub base_url: String,              // 基础URL
    pub icon: String,                  // 图标
    pub models: Vec<ModelConfig>,      // 模型配置列表
    pub enabled: bool,                 // 是否启用
    pub builtin: bool,                 // 是否内置
    pub description: String,           // 描述
}

pub struct ModelConfig {
    pub name: String,                  // 模型名称
    pub settings: Option<ModelSettings>, // 模型设置
}

pub struct ModelSettings {
    pub temperature: Option<f64>,      // 温度参数
    pub top_p: Option<f64>,           // top_p参数
    pub presence_penalty: Option<f64>, // 存在惩罚
    pub frequency_penalty: Option<f64>, // 频率惩罚
    pub max_tokens: Option<i32>,       // 最大令牌数
}
```

## 约束条件

### 技术约束
- 必须使用Rust + Axum框架
- 必须使用SurrealDB替代Elasticsearch
- 必须集成现有的认证中间件
- 必须保持API路径和响应格式不变

### 业务约束
- 内置提供商不能被删除
- 内置提供商的name和builtin字段不能修改
- API密钥等敏感信息需要安全处理

### 法规要求
- 遵循数据保护法规
- API密钥等敏感数据加密存储

## 假设条件

- SurrealDB已正确配置并可用
- 现有认证系统正常工作
- 客户端遵循现有API约定
- 网络连接稳定可靠

## 范围外

- 模型提供商的实际AI模型调用功能
- 模型提供商的健康检查和监控
- 批量导入/导出功能
- 模型提供商使用统计和分析
- 第三方集成（除了基本的API调用）

## 验收标准总结

系统成功实现当：
1. 所有CRUD操作正常工作
2. 搜索功能返回正确结果
3. 缓存机制有效提升性能
4. 现有客户端可以无缝连接
5. 所有安全要求得到满足
6. 性能指标达到要求
7. 错误处理健壮可靠
