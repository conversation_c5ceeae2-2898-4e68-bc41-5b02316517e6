# Model Provider API 技术设计文档

## 执行摘要

本文档详细描述了Model Provider API的技术架构设计，采用Rust + Axum + SurrealDB技术栈，实现与原Go版本完全兼容的API。设计遵循分层架构模式，确保代码的可维护性、可测试性和高性能。

## 架构概述

### 系统上下文图
```mermaid
C4Context
    Person(client, "Coco App Client", "前端应用")
    System(rust_api, "Rust Model Provider API", "模型提供商管理API")
    System_Ext(surrealdb, "SurrealDB", "数据存储")
    System_Ext(cache, "内存缓存", "性能优化")

    Rel(client, rust_api, "HTTPS/REST")
    Rel(rust_api, surrealdb, "SurrealDB协议")
    Rel(rust_api, cache, "内存访问")
```

### 容器架构图
```mermaid
C4Container
    Container(web, "Web层", "Axum Router", "HTTP请求路由")
    Container(handler, "处理器层", "Handler Functions", "业务逻辑处理")
    Container(service, "服务层", "Service Layer", "核心业务逻辑")
    Container(repo, "仓储层", "Repository Layer", "数据访问抽象")
    Container(db, "数据库", "SurrealDB", "数据持久化")
    Container(cache, "缓存层", "HashMap/LRU", "内存缓存")

    Rel(web, handler, "函数调用")
    Rel(handler, service, "服务调用")
    Rel(service, repo, "数据访问")
    Rel(service, cache, "缓存操作")
    Rel(repo, db, "SQL查询")
```

## 技术栈选择

### 核心技术
| 技术 | 选择 | 理由 |
|------|------|------|
| 框架 | Axum 0.7 | 高性能、类型安全、与现有代码一致 |
| 数据库 | SurrealDB | 替代Elasticsearch，支持SQL和NoSQL |
| 序列化 | Serde + serde_json | Rust生态标准，性能优秀 |
| 异步运行时 | Tokio | 成熟的异步生态系统 |
| HTTP客户端 | Reqwest | 用于外部API调用 |
| 日志 | tracing + tracing-subscriber | 结构化日志，性能优秀 |

### 依赖管理
```toml
[dependencies]
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
surrealdb = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"
```

## 组件设计

### 1. 数据模型层 (models/model_provider.rs)

```rust
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelProvider {
    pub id: String,
    pub created: DateTime<Utc>,
    pub updated: DateTime<Utc>,
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<ModelConfig>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub name: String,
    pub settings: Option<ModelSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelSettings {
    pub temperature: Option<f64>,
    pub top_p: Option<f64>,
    pub presence_penalty: Option<f64>,
    pub frequency_penalty: Option<f64>,
    pub max_tokens: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct CreateModelProviderRequest {
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: Option<String>,
    pub models: Option<Vec<ModelConfig>>,
    pub enabled: Option<bool>,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateModelProviderRequest {
    pub name: Option<String>,
    pub api_key: Option<String>,
    pub api_type: Option<String>,
    pub base_url: Option<String>,
    pub icon: Option<String>,
    pub models: Option<Vec<ModelConfig>>,
    pub enabled: Option<bool>,
    pub description: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
}
```

### 2. 仓储层 (repositories/model_provider_repo.rs)

```rust
use async_trait::async_trait;
use anyhow::Result;
use surrealdb::{Surreal, engine::remote::ws::Client};
use crate::models::model_provider::ModelProvider;
use crate::error::AppError;

#[async_trait]
pub trait ModelProviderRepository: Send + Sync {
    async fn create(&self, provider: &ModelProvider) -> Result<String>;
    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>>;
    async fn update(&self, provider: &ModelProvider) -> Result<()>;
    async fn delete(&self, id: &str) -> Result<()>;
    async fn search(&self, query: &SearchQuery) -> Result<SearchResponse>;
}

pub struct SurrealModelProviderRepository {
    db: Surreal<Client>,
}

impl SurrealModelProviderRepository {
    pub fn new(db: Surreal<Client>) -> Self {
        Self { db }
    }
}

#[async_trait]
impl ModelProviderRepository for SurrealModelProviderRepository {
    async fn create(&self, provider: &ModelProvider) -> Result<String> {
        let result: Vec<ModelProvider> = self.db
            .create("model_provider")
            .content(provider)
            .await?;
        
        Ok(result[0].id.clone())
    }

    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>> {
        let provider: Option<ModelProvider> = self.db
            .select(("model_provider", id))
            .await?;
        
        Ok(provider)
    }

    async fn update(&self, provider: &ModelProvider) -> Result<()> {
        let _: Option<ModelProvider> = self.db
            .update(("model_provider", &provider.id))
            .content(provider)
            .await?;
        
        Ok(())
    }

    async fn delete(&self, id: &str) -> Result<()> {
        let _: Option<ModelProvider> = self.db
            .delete(("model_provider", id))
            .await?;
        
        Ok(())
    }

    async fn search(&self, query: &SearchQuery) -> Result<SearchResponse> {
        // 实现搜索逻辑，支持分页、排序、过滤
        // 转换为SurrealDB查询语法
        todo!("实现搜索功能")
    }
}
```

### 3. 服务层 (services/model_provider_service.rs)

```rust
use std::sync::Arc;
use anyhow::Result;
use chrono::Utc;
use uuid::Uuid;
use crate::models::model_provider::*;
use crate::repositories::model_provider_repo::ModelProviderRepository;
use crate::services::cache_service::CacheService;
use crate::error::AppError;

pub struct ModelProviderService {
    repository: Arc<dyn ModelProviderRepository>,
    cache: Arc<CacheService>,
}

impl ModelProviderService {
    pub fn new(
        repository: Arc<dyn ModelProviderRepository>,
        cache: Arc<CacheService>,
    ) -> Self {
        Self { repository, cache }
    }

    pub async fn create_provider(&self, req: CreateModelProviderRequest) -> Result<String> {
        // 验证输入
        self.validate_create_request(&req)?;
        
        // 创建模型提供商对象
        let provider = ModelProvider {
            id: Uuid::new_v4().to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: req.name,
            api_key: req.api_key,
            api_type: req.api_type,
            base_url: req.base_url,
            icon: req.icon.unwrap_or_default(),
            models: req.models.unwrap_or_default(),
            enabled: req.enabled.unwrap_or(true),
            builtin: false, // 新创建的都不是内置的
            description: req.description.unwrap_or_default(),
        };

        // 保存到数据库
        let id = self.repository.create(&provider).await?;
        
        // 更新缓存
        self.cache.set_model_provider(&id, &provider).await;
        
        Ok(id)
    }

    pub async fn get_provider(&self, id: &str) -> Result<Option<ModelProvider>> {
        // 先检查缓存
        if let Some(provider) = self.cache.get_model_provider(id).await {
            return Ok(Some(provider));
        }

        // 从数据库获取
        let provider = self.repository.get_by_id(id).await?;
        
        // 更新缓存
        if let Some(ref p) = provider {
            self.cache.set_model_provider(id, p).await;
        }
        
        Ok(provider)
    }

    pub async fn update_provider(&self, id: &str, req: UpdateModelProviderRequest) -> Result<()> {
        // 获取现有提供商
        let mut provider = self.get_provider(id).await?
            .ok_or_else(|| AppError::NotFound(format!("Model provider {} not found", id)))?;

        // 检查是否为内置提供商的保护字段
        if provider.builtin {
            if req.name.is_some() {
                return Err(AppError::Forbidden("Cannot modify name of builtin provider".into()));
            }
        }

        // 更新字段
        if let Some(name) = req.name { provider.name = name; }
        if let Some(api_key) = req.api_key { provider.api_key = api_key; }
        if let Some(api_type) = req.api_type { provider.api_type = api_type; }
        if let Some(base_url) = req.base_url { provider.base_url = base_url; }
        if let Some(icon) = req.icon { provider.icon = icon; }
        if let Some(models) = req.models { provider.models = models; }
        if let Some(enabled) = req.enabled { provider.enabled = enabled; }
        if let Some(description) = req.description { provider.description = description; }
        
        provider.updated = Utc::now();

        // 保存到数据库
        self.repository.update(&provider).await?;
        
        // 清除缓存
        self.cache.delete_model_provider(id).await;
        
        Ok(())
    }

    pub async fn delete_provider(&self, id: &str) -> Result<()> {
        // 获取提供商信息
        let provider = self.get_provider(id).await?
            .ok_or_else(|| AppError::NotFound(format!("Model provider {} not found", id)))?;

        // 检查是否为内置提供商
        if provider.builtin {
            return Err(AppError::Forbidden("Built-in model providers cannot be deleted".into()));
        }

        // 从数据库删除
        self.repository.delete(id).await?;
        
        // 清除缓存
        self.cache.delete_model_provider(id).await;
        
        Ok(())
    }

    fn validate_create_request(&self, req: &CreateModelProviderRequest) -> Result<()> {
        if req.name.trim().is_empty() {
            return Err(AppError::BadRequest("Name is required".into()));
        }
        
        if req.api_type.trim().is_empty() {
            return Err(AppError::BadRequest("API type is required".into()));
        }
        
        if !["openai", "ollama"].contains(&req.api_type.as_str()) {
            return Err(AppError::BadRequest("Invalid API type".into()));
        }
        
        if req.base_url.trim().is_empty() {
            return Err(AppError::BadRequest("Base URL is required".into()));
        }
        
        Ok(())
    }
}
```

### 4. 处理器层 (handlers/model_provider_handler.rs)

```rust
use std::sync::Arc;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Json as JsonExtractor,
};
use serde_json::Value;
use crate::models::model_provider::*;
use crate::services::model_provider_service::ModelProviderService;
use crate::error::{AppError, AppResult};

pub struct ModelProviderHandler {
    service: Arc<ModelProviderService>,
}

impl ModelProviderHandler {
    pub fn new(service: Arc<ModelProviderService>) -> Self {
        Self { service }
    }

    pub async fn create_provider(
        &self,
        JsonExtractor(req): JsonExtractor<CreateModelProviderRequest>,
    ) -> AppResult<Json<ApiResponse<()>>> {
        let id = self.service.create_provider(req).await?;
        
        Ok(Json(ApiResponse {
            id,
            result: "created".to_string(),
            data: None,
        }))
    }

    pub async fn get_provider(
        &self,
        Path(id): Path<String>,
    ) -> AppResult<Json<Value>> {
        let provider = self.service.get_provider(&id).await?
            .ok_or_else(|| AppError::NotFound(format!("Model provider {} not found", id)))?;

        // 过滤敏感字段
        let mut response = serde_json::to_value(&provider)?;
        if let Some(obj) = response.as_object_mut() {
            obj.remove("api_key"); // 在某些情况下过滤API密钥
        }

        Ok(Json(response))
    }

    pub async fn update_provider(
        &self,
        Path(id): Path<String>,
        JsonExtractor(req): JsonExtractor<UpdateModelProviderRequest>,
    ) -> AppResult<Json<ApiResponse<()>>> {
        self.service.update_provider(&id, req).await?;
        
        Ok(Json(ApiResponse {
            id,
            result: "updated".to_string(),
            data: None,
        }))
    }

    pub async fn delete_provider(
        &self,
        Path(id): Path<String>,
    ) -> AppResult<Json<ApiResponse<()>>> {
        self.service.delete_provider(&id).await?;
        
        Ok(Json(ApiResponse {
            id,
            result: "deleted".to_string(),
            data: None,
        }))
    }

    pub async fn search_providers(
        &self,
        Query(params): Query<SearchParams>,
        body: Option<JsonExtractor<Value>>,
    ) -> AppResult<Json<Value>> {
        // 构建搜索查询
        let query = self.build_search_query(params, body)?;
        
        // 执行搜索
        let response = self.service.search_providers(query).await?;
        
        Ok(Json(serde_json::to_value(response)?))
    }

    fn build_search_query(
        &self,
        params: SearchParams,
        body: Option<JsonExtractor<Value>>,
    ) -> AppResult<SearchQuery> {
        // 实现搜索查询构建逻辑
        // 支持URL参数和请求体查询
        todo!("实现搜索查询构建")
    }
}
```

## 路由配置

### API路由注册 (main.rs)
```rust
// 在main.rs中添加路由
let model_provider_handler = Arc::new(ModelProviderHandler::new(model_provider_service));

let api_app = Router::new()
    // 现有路由...
    .route("/model_provider/", post({
        let handler = model_provider_handler.clone();
        move |req| async move { handler.create_provider(req).await }
    }))
    .route("/model_provider/:id", get({
        let handler = model_provider_handler.clone();
        move |path| async move { handler.get_provider(path).await }
    }))
    .route("/model_provider/:id", put({
        let handler = model_provider_handler.clone();
        move |path, req| async move { handler.update_provider(path, req).await }
    }))
    .route("/model_provider/:id", delete({
        let handler = model_provider_handler.clone();
        move |path| async move { handler.delete_provider(path).await }
    }))
    .route("/model_provider/_search", get({
        let handler = model_provider_handler.clone();
        move |query| async move { handler.search_providers(query, None).await }
    }))
    .route("/model_provider/_search", post({
        let handler = model_provider_handler.clone();
        move |query, body| async move { handler.search_providers(query, Some(body)).await }
    }));
```

## 数据库设计

### SurrealDB表结构
```sql
-- 定义model_provider表
DEFINE TABLE model_provider SCHEMAFULL;

-- 定义字段
DEFINE FIELD id ON model_provider TYPE string;
DEFINE FIELD created ON model_provider TYPE datetime;
DEFINE FIELD updated ON model_provider TYPE datetime;
DEFINE FIELD name ON model_provider TYPE string;
DEFINE FIELD api_key ON model_provider TYPE string;
DEFINE FIELD api_type ON model_provider TYPE string;
DEFINE FIELD base_url ON model_provider TYPE string;
DEFINE FIELD icon ON model_provider TYPE string;
DEFINE FIELD models ON model_provider TYPE array;
DEFINE FIELD enabled ON model_provider TYPE bool;
DEFINE FIELD builtin ON model_provider TYPE bool;
DEFINE FIELD description ON model_provider TYPE string;

-- 定义索引
DEFINE INDEX name_idx ON model_provider COLUMNS name;
DEFINE INDEX enabled_idx ON model_provider COLUMNS enabled;
DEFINE INDEX builtin_idx ON model_provider COLUMNS builtin;
DEFINE INDEX api_type_idx ON model_provider COLUMNS api_type;
```

## 缓存策略

### 缓存设计
- **缓存键格式**: `model_provider:{id}`
- **缓存时间**: 30分钟
- **缓存更新策略**: Write-through（写入时更新）
- **缓存失效策略**: 更新/删除时主动清除

### 缓存实现
```rust
use std::collections::HashMap;
use std::sync::RwLock;
use chrono::{DateTime, Utc, Duration};

pub struct CacheService {
    cache: RwLock<HashMap<String, CacheItem<ModelProvider>>>,
}

struct CacheItem<T> {
    data: T,
    expires_at: DateTime<Utc>,
}

impl CacheService {
    pub async fn get_model_provider(&self, id: &str) -> Option<ModelProvider> {
        let cache = self.cache.read().unwrap();
        let key = format!("model_provider:{}", id);
        
        if let Some(item) = cache.get(&key) {
            if item.expires_at > Utc::now() {
                return Some(item.data.clone());
            }
        }
        
        None
    }

    pub async fn set_model_provider(&self, id: &str, provider: &ModelProvider) {
        let mut cache = self.cache.write().unwrap();
        let key = format!("model_provider:{}", id);
        let expires_at = Utc::now() + Duration::minutes(30);
        
        cache.insert(key, CacheItem {
            data: provider.clone(),
            expires_at,
        });
    }

    pub async fn delete_model_provider(&self, id: &str) {
        let mut cache = self.cache.write().unwrap();
        let key = format!("model_provider:{}", id);
        cache.remove(&key);
    }
}
```

## 错误处理

### 错误类型定义
```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("Not found: {0}")]
    NotFound(String),
    
    #[error("Bad request: {0}")]
    BadRequest(String),
    
    #[error("Forbidden: {0}")]
    Forbidden(String),
    
    #[error("Internal server error: {0}")]
    Internal(String),
    
    #[error("Database error: {0}")]
    Database(#[from] surrealdb::Error),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            AppError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            AppError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            AppError::Forbidden(msg) => (StatusCode::FORBIDDEN, msg),
            _ => (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error".to_string()),
        };

        let body = Json(serde_json::json!({
            "error": message
        }));

        (status, body).into_response()
    }
}
```

## 安全设计

### 认证和授权
- 集成现有的认证中间件
- 实现基于角色的权限控制
- API密钥字段加密存储
- 敏感字段响应过滤

### 输入验证
- 请求参数验证
- SQL注入防护
- XSS防护
- 数据长度限制

## 性能优化

### 查询优化
- 数据库索引优化
- 查询语句优化
- 分页查询支持
- 结果集缓存

### 并发处理
- 异步处理所有I/O操作
- 连接池管理
- 请求限流
- 优雅关闭

## 监控和日志

### 日志记录
```rust
use tracing::{info, warn, error, instrument};

#[instrument(skip(self))]
pub async fn create_provider(&self, req: CreateModelProviderRequest) -> Result<String> {
    info!("Creating model provider: {}", req.name);
    
    match self.repository.create(&provider).await {
        Ok(id) => {
            info!("Model provider created successfully: {}", id);
            Ok(id)
        }
        Err(e) => {
            error!("Failed to create model provider: {}", e);
            Err(e)
        }
    }
}
```

### 指标收集
- 请求响应时间
- 错误率统计
- 缓存命中率
- 数据库查询性能

## 测试策略

### 单元测试
- 服务层逻辑测试
- 数据验证测试
- 错误处理测试

### 集成测试
- API端点测试
- 数据库集成测试
- 缓存功能测试

### 性能测试
- 负载测试
- 压力测试
- 并发测试

## 部署考虑

### 配置管理
- 环境变量配置
- 配置文件管理
- 敏感信息加密

### 健康检查
- 数据库连接检查
- 缓存状态检查
- 服务可用性检查

设计阶段看起来好吗？[是/否/修订]
